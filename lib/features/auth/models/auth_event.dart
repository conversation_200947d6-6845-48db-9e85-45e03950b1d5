import 'package:equatable/equatable.dart';

abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

class AuthCheckRequested extends AuthEvent {}

class AuthLoginRequested extends AuthEvent {
  final String email;
  final String password;

  const AuthLoginRequested({
    required this.email,
    required this.password,
  });

  @override
  List<Object?> get props => [email, password];
}

class AuthBiometricLoginRequested extends AuthEvent {
  final String email;

  const AuthBiometricLoginRequested({required this.email});

  @override
  List<Object?> get props => [email];
}

class AuthTwoFactorSubmitted extends AuthEvent {
  final String referenceCode;
  final String twoFactorCode;

  const AuthTwoFactorSubmitted({
    required this.referenceCode,
    required this.twoFactorCode,
  });

  @override
  List<Object?> get props => [referenceCode, twoFactorCode];
}

class AuthLogoutRequested extends AuthEvent {}

class AuthTokenRefreshRequested extends AuthEvent {}

class AuthTwoFactorToggled extends AuthEvent {
  final bool enabled;

  const AuthTwoFactorToggled({required this.enabled});

  @override
  List<Object?> get props => [enabled];
}

class AuthBiometricToggled extends AuthEvent {
  final bool enabled;

  const AuthBiometricToggled({required this.enabled});

  @override
  List<Object?> get props => [enabled];
}

class AuthSignUpRequested extends AuthEvent {
  final String actCode;
  final String fullName;
  final String email;
  final String phone;

  const AuthSignUpRequested({
    required this.actCode,
    required this.fullName,
    required this.email,
    required this.phone,
  });

  @override
  List<Object?> get props => [actCode, fullName, email, phone];
}

class AuthContactVerificationRequested extends AuthEvent {
  final String actCode;
  final String password;
  final String emailCode;
  final String phoneCode;

  const AuthContactVerificationRequested({
    required this.actCode,
    required this.password,
    required this.emailCode,
    required this.phoneCode,
  });

  @override
  List<Object?> get props => [actCode, password, emailCode, phoneCode];
}

class AuthProfileUpdateRequested extends AuthEvent {
  final String oldPassword;
  final String fullName;
  final String email;
  final String phone;
  final String newPassword;

  const AuthProfileUpdateRequested({
    required this.oldPassword,
    required this.fullName,
    required this.email,
    required this.phone,
    required this.newPassword,
  });

  @override
  List<Object?> get props => [oldPassword, fullName, email, phone, newPassword];
}
