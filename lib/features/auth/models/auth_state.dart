import 'package:equatable/equatable.dart';
import 'auth_user.dart';

abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

class AuthInitial extends AuthState {}

class AuthLoading extends AuthState {}

class AuthAuthenticated extends AuthState {
  final AuthUser user;
  final bool isTwoFactorEnabled;
  final bool isBiometricEnabled;

  const AuthAuthenticated({
    required this.user,
    required this.isTwoFactorEnabled,
    required this.isBiometricEnabled,
  });

  @override
  List<Object?> get props => [user, isTwoFactorEnabled, isBiometricEnabled];

  AuthAuthenticated copyWith({
    AuthUser? user,
    bool? isTwoFactorEnabled,
    bool? isBiometricEnabled,
  }) {
    return AuthAuthenticated(
      user: user ?? this.user,
      isTwoFactorEnabled: isTwoFactorEnabled ?? this.isTwoFactorEnabled,
      isBiometricEnabled: isBiometricEnabled ?? this.isBiometricEnabled,
    );
  }
}

class AuthUnauthenticated extends AuthState {}

class AuthTwoFactorRequired extends AuthState {
  final String referenceCode;

  const AuthTwoFactorRequired({required this.referenceCode});

  @override
  List<Object?> get props => [referenceCode];
}

class AuthError extends AuthState {
  final String message;

  const AuthError({required this.message});

  @override
  List<Object?> get props => [message];
}

class AuthBiometricPrompt extends AuthState {
  final String email;

  const AuthBiometricPrompt({required this.email});

  @override
  List<Object?> get props => [email];
}
