// Export all auth models from a single file for easier imports
export 'auth_user.dart';
export 'auth_state.dart';
export 'auth_event.dart';

/// Authentication token information
class AuthTokens {
  final String accessToken;
  final String refreshToken;

  const AuthTokens({
    required this.accessToken,
    required this.refreshToken,
  });

  factory AuthTokens.fromResponse(String response) {
    final splitResponse = response.split('|');
    if (splitResponse.length == 2) {
      return AuthTokens(
        accessToken: splitResponse[0],
        refreshToken: splitResponse[1],
      );
    }
    throw Exception('Invalid token response format');
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthTokens &&
        other.accessToken == accessToken &&
        other.refreshToken == refreshToken;
  }

  @override
  int get hashCode => Object.hash(accessToken, refreshToken);

  @override
  String toString() => 'AuthTokens(accessToken: ${accessToken.substring(0, 10)}..., refreshToken: ${refreshToken.substring(0, 10)}...)';
}

/// Two-factor authentication setup response
class TwoFactorSetupResponse {
  final String qrCodeImage;
  final String setupUrl;

  const TwoFactorSetupResponse({
    required this.qrCodeImage,
    required this.setupUrl,
  });

  factory TwoFactorSetupResponse.fromResponse(String response) {
    final splitResponse = response.split('|');
    if (splitResponse.length >= 2) {
      return TwoFactorSetupResponse(
        qrCodeImage: splitResponse[0],
        setupUrl: splitResponse[1],
      );
    }
    throw Exception('Invalid 2FA setup response format');
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TwoFactorSetupResponse &&
        other.qrCodeImage == qrCodeImage &&
        other.setupUrl == setupUrl;
  }

  @override
  int get hashCode => Object.hash(qrCodeImage, setupUrl);
}

/// Authentication configuration
class AuthConfig {
  static const String au1Url = 'https://api.nudron.com/prod/au1';
  static const String au3Url = 'https://api.nudron.com/prod/au3';
  static const String tenantId = "d14b3819-5e90-4b1e-8821-9fcb72684627";
  static const String clientId = "WaterMeteringMobile2";
  
  // Secure storage keys
  static const String accessTokenKey = 'access_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String emailKey = 'email';
  static const String passwordKey = 'password';
  static const String twoFactorKey = 'two_factor';
  static const String biometricKey = 'biometric';
}
