class AuthUser {
  final String id;
  final String name;
  final String email;
  final bool emailVerified;
  final String phone;
  final bool phoneVerified;
  final String? lastPassChange;
  final int? lastUpdate;
  final String? multiFactor;

  const AuthUser({
    required this.id,
    required this.name,
    required this.email,
    required this.emailVerified,
    required this.phone,
    required this.phoneVerified,
    this.lastPassChange,
    this.lastUpdate,
    this.multiFactor,
  });

  factory AuthUser.fromJson(Map<String, dynamic> json) {
    return AuthUser(
      id: json['userID'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      emailVerified: json['emailVerified'] ?? false,
      phone: json['phone'] ?? '',
      phoneVerified: json['phoneVerified'] ?? false,
      lastPassChange: json['lastPassChange'],
      lastUpdate: json['lastUpdate'],
      multiFactor: json['multiFactor'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userID': id,
      'name': name,
      'email': email,
      'emailVerified': emailVerified,
      'phone': phone,
      'phoneVerified': phoneVerified,
      'lastPassChange': lastPassChange,
      'lastUpdate': lastUpdate,
      'multiFactor': multiFactor,
    };
  }

  AuthUser copyWith({
    String? id,
    String? name,
    String? email,
    bool? emailVerified,
    String? phone,
    bool? phoneVerified,
    String? lastPassChange,
    int? lastUpdate,
    String? multiFactor,
  }) {
    return AuthUser(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      emailVerified: emailVerified ?? this.emailVerified,
      phone: phone ?? this.phone,
      phoneVerified: phoneVerified ?? this.phoneVerified,
      lastPassChange: lastPassChange ?? this.lastPassChange,
      lastUpdate: lastUpdate ?? this.lastUpdate,
      multiFactor: multiFactor ?? this.multiFactor,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthUser &&
        other.id == id &&
        other.name == name &&
        other.email == email &&
        other.emailVerified == emailVerified &&
        other.phone == phone &&
        other.phoneVerified == phoneVerified &&
        other.lastPassChange == lastPassChange &&
        other.lastUpdate == lastUpdate &&
        other.multiFactor == multiFactor;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      name,
      email,
      emailVerified,
      phone,
      phoneVerified,
      lastPassChange,
      lastUpdate,
      multiFactor,
    );
  }

  @override
  String toString() {
    return 'AuthUser(id: $id, name: $name, email: $email, emailVerified: $emailVerified, phone: $phone, phoneVerified: $phoneVerified)';
  }
}
