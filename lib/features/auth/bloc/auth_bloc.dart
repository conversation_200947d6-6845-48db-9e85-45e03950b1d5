import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../models/auth_models.dart';
import '../repository/auth_repository.dart';
import '../service/auth_service.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthRepository _authRepository;
  final AuthService _authService;

  AuthBloc({
    AuthRepository? authRepository,
    AuthService? authService,
  })  : _authRepository = authRepository ?? AuthRepository(),
        _authService = authService ?? AuthService(),
        super(AuthInitial()) {
    on<AuthCheckRequested>(_onAuthCheckRequested);
    on<AuthLoginRequested>(_onAuthLoginRequested);
    on<AuthBiometricLoginRequested>(_onAuthBiometricLoginRequested);
    on<AuthTwoFactorSubmitted>(_onAuthTwoFactorSubmitted);
    on<AuthLogoutRequested>(_onAuthLogoutRequested);
    on<AuthTokenRefreshRequested>(_onAuthTokenRefreshRequested);
    on<AuthTwoFactorToggled>(_onAuthTwoFactorToggled);
    on<AuthBiometricToggled>(_onAuthBiometricToggled);
    on<AuthSignUpRequested>(_onAuthSignUpRequested);
    on<AuthContactVerificationRequested>(_onAuthContactVerificationRequested);
    on<AuthProfileUpdateRequested>(_onAuthProfileUpdateRequested);
  }

  Future<void> _onAuthCheckRequested(
    AuthCheckRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthLoading());
      
      // Check if user is authenticated
      bool isAuthenticated = await _authRepository.isAuthenticated();
      if (!isAuthenticated) {
        // Check for biometric login option
        String? biometricEmail = await _authService.getBiometricLoginEmail();
        if (biometricEmail != null) {
          emit(AuthBiometricPrompt(email: biometricEmail));
        } else {
          emit(AuthUnauthenticated());
        }
        return;
      }

      // Get user profile
      final profileData = await _authRepository.getProfile();
      final user = AuthUser.fromJson(profileData['profile']);
      
      // Get auth settings
      final isTwoFactorEnabled = await _authService.isTwoFactorEnabled();
      final isBiometricEnabled = await _authService.isBiometricEnabled();

      emit(AuthAuthenticated(
        user: user,
        isTwoFactorEnabled: isTwoFactorEnabled,
        isBiometricEnabled: isBiometricEnabled,
      ));
    } catch (e) {
      if (kDebugMode) {
        print('Auth check error: $e');
      }
      emit(AuthUnauthenticated());
    }
  }

  Future<void> _onAuthLoginRequested(
    AuthLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthLoading());
      
      String? twoFactorRef = await _authRepository.login(event.email, event.password);
      
      if (twoFactorRef != null) {
        // 2FA required
        emit(AuthTwoFactorRequired(referenceCode: twoFactorRef));
      } else {
        // Login successful, get user profile
        final profileData = await _authRepository.getProfile();
        final user = AuthUser.fromJson(profileData['profile']);
        
        final isTwoFactorEnabled = await _authService.isTwoFactorEnabled();
        final isBiometricEnabled = await _authService.isBiometricEnabled();

        emit(AuthAuthenticated(
          user: user,
          isTwoFactorEnabled: isTwoFactorEnabled,
          isBiometricEnabled: isBiometricEnabled,
        ));
      }
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  Future<void> _onAuthBiometricLoginRequested(
    AuthBiometricLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthLoading());
      
      // Authenticate with biometrics
      bool authenticated = await _authService.authenticateWithBiometrics(
        reason: 'Authenticate to login',
      );
      
      if (!authenticated) {
        emit(AuthError(message: 'Biometric authentication failed'));
        return;
      }

      // Get stored password and login
      String? password = await _authService.getStoredPassword();
      if (password == null) {
        emit(AuthError(message: 'No stored credentials found'));
        return;
      }

      String? twoFactorRef = await _authRepository.login(event.email, password);
      
      if (twoFactorRef != null) {
        emit(AuthTwoFactorRequired(referenceCode: twoFactorRef));
      } else {
        final profileData = await _authRepository.getProfile();
        final user = AuthUser.fromJson(profileData['profile']);
        
        final isTwoFactorEnabled = await _authService.isTwoFactorEnabled();
        final isBiometricEnabled = await _authService.isBiometricEnabled();

        emit(AuthAuthenticated(
          user: user,
          isTwoFactorEnabled: isTwoFactorEnabled,
          isBiometricEnabled: isBiometricEnabled,
        ));
      }
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  Future<void> _onAuthTwoFactorSubmitted(
    AuthTwoFactorSubmitted event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthLoading());
      
      await _authRepository.submitTwoFactorCode(event.referenceCode, event.twoFactorCode);
      
      // Get user profile after successful 2FA
      final profileData = await _authRepository.getProfile();
      final user = AuthUser.fromJson(profileData['profile']);
      
      final isTwoFactorEnabled = await _authService.isTwoFactorEnabled();
      final isBiometricEnabled = await _authService.isBiometricEnabled();

      emit(AuthAuthenticated(
        user: user,
        isTwoFactorEnabled: isTwoFactorEnabled,
        isBiometricEnabled: isBiometricEnabled,
      ));
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  Future<void> _onAuthLogoutRequested(
    AuthLogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthLoading());
      await _authRepository.logout();
      emit(AuthUnauthenticated());
    } catch (e) {
      if (kDebugMode) {
        print('Logout error: $e');
      }
      // Even if logout API fails, clear local state
      emit(AuthUnauthenticated());
    }
  }

  Future<void> _onAuthTokenRefreshRequested(
    AuthTokenRefreshRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      await _authRepository.refreshAccessToken();
      // Token refreshed successfully, no state change needed
    } catch (e) {
      // Token refresh failed, logout user
      emit(AuthUnauthenticated());
    }
  }

  Future<void> _onAuthTwoFactorToggled(
    AuthTwoFactorToggled event,
    Emitter<AuthState> emit,
  ) async {
    if (state is AuthAuthenticated) {
      final currentState = state as AuthAuthenticated;
      await _authService.setTwoFactorEnabled(event.enabled);
      
      emit(currentState.copyWith(isTwoFactorEnabled: event.enabled));
    }
  }

  Future<void> _onAuthBiometricToggled(
    AuthBiometricToggled event,
    Emitter<AuthState> emit,
  ) async {
    if (state is AuthAuthenticated) {
      final currentState = state as AuthAuthenticated;
      await _authService.setBiometricEnabled(event.enabled);
      
      emit(currentState.copyWith(isBiometricEnabled: event.enabled));
    }
  }

  Future<void> _onAuthSignUpRequested(
    AuthSignUpRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthLoading());
      
      int timestamp = await _authRepository.signUp(
        event.actCode,
        event.fullName,
        event.email,
        event.phone,
      );
      
      // Sign up successful, but user needs to verify contact info
      // For now, just emit unauthenticated state
      emit(AuthUnauthenticated());
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  Future<void> _onAuthContactVerificationRequested(
    AuthContactVerificationRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthLoading());
      
      String result = await _authRepository.verifyContact(
        event.actCode,
        event.password,
        event.emailCode,
        event.phoneCode,
      );
      
      // Contact verification successful
      emit(AuthUnauthenticated());
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  Future<void> _onAuthProfileUpdateRequested(
    AuthProfileUpdateRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      if (state is! AuthAuthenticated) return;
      
      emit(AuthLoading());
      
      await _authRepository.updateProfile(
        event.oldPassword,
        event.fullName,
        event.email,
        event.phone,
        event.newPassword,
      );
      
      // Refresh user profile
      final profileData = await _authRepository.getProfile();
      final user = AuthUser.fromJson(profileData['profile']);
      
      final currentState = state as AuthAuthenticated;
      emit(currentState.copyWith(user: user));
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  /// Helper method to get current user
  AuthUser? get currentUser {
    if (state is AuthAuthenticated) {
      return (state as AuthAuthenticated).user;
    }
    return null;
  }

  /// Helper method to check if user is authenticated
  bool get isAuthenticated => state is AuthAuthenticated;

  /// Helper method to get valid access token
  Future<String?> getValidAccessToken() async {
    try {
      return await _authService.getValidAccessToken();
    } catch (e) {
      // Token refresh needed, trigger refresh
      add(AuthTokenRefreshRequested());
      return null;
    }
  }
}
