import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

import '../../../changeNotifiers/NudronChangeNotifiers.dart';
import '../../../main.dart';
import '../../../theme/theme2.dart';
import '../../../utils/alert_message.dart';
import '../../../utils/new_loader.dart';
import '../../../views/widgets/containers/customButton.dart';
import '../../../views/widgets/containers/customTextField.dart';
import '../../../views/pages/RegisterPage.dart';
import '../bloc/auth_bloc.dart';
import '../models/auth_models.dart';
import '../service/auth_service.dart';
import 'enter_two_fac_code.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final AuthService _authService = AuthService();

  @override
  void initState() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitDown,
      DeviceOrientation.portraitUp,
    ]);
    NudronRandomStuff.isSignIn.addListener(() {
      setState(() {});
    });
    super.initState();
  }

  @override
  void dispose() {
    emailController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  List<Widget> get pages => [
    const SigninPage(),
    const RegisterPage(),
  ];

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthAuthenticated) {
          CustomAlert.showCustomScaffoldMessenger(
            context,
            "Successfully logged in!",
            AlertType.success,
          );
          Navigator.of(context).pushNamedAndRemoveUntil("/", (route) => false);
        } else if (state is AuthTwoFactorRequired) {
          CustomAlert.showCustomScaffoldMessenger(
            context,
            "Please enter the code sent to your authenticator app/sms",
            AlertType.info,
          );
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => EnterTwoFacCode(
                referenceCode: state.referenceCode,
              ),
            ),
          );
        } else if (state is AuthError) {
          CustomAlert.showCustomScaffoldMessenger(
            context,
            state.message,
            AlertType.error,
          );
        } else if (state is AuthBiometricPrompt) {
          _showBiometricDialog(state.email);
        }
      },
      child: Scaffold(
        backgroundColor: Provider.of<ThemeNotifier>(context).currentTheme.bgColor,
        body: SafeArea(
          child: IndexedStack(
            index: NudronRandomStuff.isSignIn.value ? 0 : 1,
            children: pages,
          ),
        ),
      ),
    );
  }

  void _showBiometricDialog(String email) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Biometric Login'),
          content: Text('Use biometric authentication to login as $email?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('NO'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                context.read<AuthBloc>().add(
                  AuthBiometricLoginRequested(email: email),
                );
              },
              child: Text('YES'),
            ),
          ],
        );
      },
    );
  }
}

class SigninPage extends StatefulWidget {
  const SigninPage({super.key});

  @override
  State<SigninPage> createState() => _SigninPageState();
}

class _SigninPageState extends State<SigninPage> {
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  bool _obscurePassword = true;

  @override
  void dispose() {
    emailController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        return Padding(
          padding: EdgeInsets.all(20.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo or title
              Text(
                'Water Metering',
                style: GoogleFonts.roboto(
                  fontSize: 32.sp,
                  fontWeight: FontWeight.bold,
                  color: Provider.of<ThemeNotifier>(context)
                      .currentTheme
                      .basicAdvanceTextColor,
                ),
              ),
              SizedBox(height: 50.h),
              
              // Email field
              CustomTextField(
                controller: emailController,
                hintText: 'Email',
                keyboardType: TextInputType.emailAddress,
              ),
              SizedBox(height: 20.h),
              
              // Password field
              CustomTextField(
                controller: passwordController,
                hintText: 'Password',
                obscureText: _obscurePassword,
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscurePassword ? Icons.visibility : Icons.visibility_off,
                  ),
                  onPressed: () {
                    setState(() {
                      _obscurePassword = !_obscurePassword;
                    });
                  },
                ),
              ),
              SizedBox(height: 30.h),
              
              // Login button
              if (state is AuthLoading)
                CircularProgressIndicator()
              else
                CustomButton(
                  text: "LOGIN",
                  onPressed: () {
                    if (emailController.text.isEmpty || passwordController.text.isEmpty) {
                      CustomAlert.showCustomScaffoldMessenger(
                        context,
                        "Please fill in all fields",
                        AlertType.error,
                      );
                      return;
                    }
                    
                    context.read<AuthBloc>().add(
                      AuthLoginRequested(
                        email: emailController.text,
                        password: passwordController.text,
                      ),
                    );
                  },
                ),
              
              SizedBox(height: 20.h),
              
              // Sign up toggle
              TextButton(
                onPressed: () {
                  NudronRandomStuff.isSignIn.value = false;
                },
                child: Text(
                  "Don't have an account? Sign up",
                  style: GoogleFonts.roboto(
                    color: Provider.of<ThemeNotifier>(context)
                        .currentTheme
                        .basicAdvanceTextColor,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
