import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:provider/provider.dart';

import '../../../theme/theme2.dart';
import '../../../utils/alert_message.dart';
import '../../../views/widgets/containers/CustomAppBar.dart';
import '../../../views/widgets/containers/customButton.dart';
import '../bloc/auth_bloc.dart';
import '../models/auth_models.dart';

class EnterTwoFacCode extends StatefulWidget {
  const EnterTwoFacCode({super.key, required this.referenceCode});

  final String referenceCode;

  @override
  State<EnterTwoFacCode> createState() => _EnterTwoFacCodeState();
}

class _EnterTwoFacCodeState extends State<EnterTwoFacCode> {
  final TextEditingController otpFieldController = TextEditingController();

  @override
  void dispose() {
    otpFieldController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthAuthenticated) {
          CustomAlert.showCustomScaffoldMessenger(
            context,
            "Successfully logged in! Redirecting to home page...",
            AlertType.success,
          );
          Navigator.of(context).pushNamedAndRemoveUntil("/", (route) => false);
        } else if (state is AuthError) {
          CustomAlert.showCustomScaffoldMessenger(
            context,
            state.message,
            AlertType.error,
          );
        }
      },
      child: Scaffold(
        backgroundColor: Provider.of<ThemeNotifier>(context).currentTheme.bgColor,
        appBar: CustomAppBar(
          title: "Two-Factor Authentication",
          showBackButton: true,
        ),
        body: BlocBuilder<AuthBloc, AuthState>(
          builder: (context, state) {
            return Padding(
              padding: EdgeInsets.all(20.w),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Title
                  Text(
                    'Enter Verification Code',
                    style: GoogleFonts.roboto(
                      fontSize: 24.sp,
                      fontWeight: FontWeight.bold,
                      color: Provider.of<ThemeNotifier>(context)
                          .currentTheme
                          .basicAdvanceTextColor,
                    ),
                  ),
                  SizedBox(height: 20.h),
                  
                  // Subtitle
                  Text(
                    'Please enter the 6-digit code sent to your authenticator app or SMS',
                    textAlign: TextAlign.center,
                    style: GoogleFonts.roboto(
                      fontSize: 16.sp,
                      color: Provider.of<ThemeNotifier>(context)
                          .currentTheme
                          .basicAdvanceTextColor
                          .withOpacity(0.7),
                    ),
                  ),
                  SizedBox(height: 40.h),
                  
                  // PIN code field
                  PinCodeTextField(
                    length: 6,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
                    ],
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    cursorColor: Provider.of<ThemeNotifier>(context)
                        .currentTheme
                        .basicAdvanceTextColor,
                    obscureText: false,
                    animationType: AnimationType.fade,
                    textStyle: GoogleFonts.roboto(
                      color: Provider.of<ThemeNotifier>(context)
                          .currentTheme
                          .basicAdvanceTextColor,
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w400,
                    ),
                    pinTheme: PinTheme(
                      shape: PinCodeFieldShape.box,
                      borderRadius: BorderRadius.circular(8.r),
                      fieldHeight: 50.h,
                      fieldWidth: 45.w,
                      activeFillColor: Provider.of<ThemeNotifier>(context)
                          .currentTheme
                          .bgColor,
                      inactiveFillColor: Provider.of<ThemeNotifier>(context)
                          .currentTheme
                          .bgColor,
                      selectedFillColor: Provider.of<ThemeNotifier>(context)
                          .currentTheme
                          .bgColor,
                      activeColor: Provider.of<ThemeNotifier>(context)
                          .currentTheme
                          .basicAdvanceTextColor,
                      inactiveColor: Provider.of<ThemeNotifier>(context)
                          .currentTheme
                          .basicAdvanceTextColor
                          .withOpacity(0.3),
                      selectedColor: Provider.of<ThemeNotifier>(context)
                          .currentTheme
                          .basicAdvanceTextColor,
                    ),
                    enableActiveFill: true,
                    animationDuration: const Duration(milliseconds: 300),
                    backgroundColor: Provider.of<ThemeNotifier>(context)
                        .currentTheme
                        .bgColor,
                    controller: otpFieldController,
                    enablePinAutofill: true,
                    onCompleted: (v) {
                      if (kDebugMode) {
                        print("OTP Completed: $v");
                      }
                    },
                    beforeTextPaste: (text) {
                      return true;
                    },
                    appContext: context,
                    onChanged: (String value) {},
                  ),
                  SizedBox(height: 40.h),
                  
                  // Verify button
                  if (state is AuthLoading)
                    CircularProgressIndicator()
                  else
                    CustomButton(
                      text: "VERIFY",
                      onPressed: () {
                        if (otpFieldController.text.length == 6) {
                          context.read<AuthBloc>().add(
                            AuthTwoFactorSubmitted(
                              referenceCode: widget.referenceCode,
                              twoFactorCode: otpFieldController.text,
                            ),
                          );
                        } else {
                          CustomAlert.showCustomScaffoldMessenger(
                            context,
                            "Please enter a valid 6-digit code",
                            AlertType.error,
                          );
                        }
                      },
                    ),
                  
                  SizedBox(height: 20.h),
                  
                  // Resend code button
                  TextButton(
                    onPressed: () {
                      // TODO: Implement resend code functionality
                      CustomAlert.showCustomScaffoldMessenger(
                        context,
                        "Resend functionality not implemented yet",
                        AlertType.info,
                      );
                    },
                    child: Text(
                      "Didn't receive the code? Resend",
                      style: GoogleFonts.roboto(
                        color: Provider.of<ThemeNotifier>(context)
                            .currentTheme
                            .basicAdvanceTextColor,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
