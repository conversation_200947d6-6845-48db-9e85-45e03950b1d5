import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:in_app_update/in_app_update.dart';
import 'package:sms_autofill/sms_autofill.dart';

import '../../../config.dart';
import '../../../utils/getDeviceID.dart';
import '../../../view_model/custom_exception.dart';
import '../models/auth_models.dart';
import '../service/auth_service.dart';

class AuthRepository {
  final AuthService _authService;

  AuthRepository({AuthService? authService}) : _authService = authService ?? AuthService();

  /// Login with email and password
  /// Returns null for successful login, or reference code for 2FA
  Future<String?> login(String email, String password) async {
    if (ConfigurationCustom.skipAnyAuths) {
      return null;
    }

    final passwordBase64 = base64.encode(utf8.encode(password));
    final body = '02$email|$passwordBase64';

    final response = await _makeRequest(body);
    final splitResponse = response.split('|');

    if (response == '0') {
      throw CustomException('Incorrect email or password');
    } else if (response == '10' || response == '01' || response == '00') {
      throw CustomException('Email or phone unverified');
    } else if (splitResponse.length == 2) {
      // Successful login with tokens
      final tokens = AuthTokens.fromResponse(response);
      await _authService.storeTokens(tokens);
      await _authService.checkAndStoreBiometric(email, password);
      await _authService.setTwoFactorEnabled(false);
      return null;
    } else if (splitResponse.length == 1) {
      // 2FA required
      try {
        await SmsAutoFill().listenForCode();
      } catch (e) {
        if (kDebugMode) {
          print("Error in parsing the OTP: $e");
        }
      }
      await _authService.checkAndStoreBiometric(email, password);
      return response; // Return reference code for 2FA
    } else {
      if (kDebugMode) {
        print('Unexpected login response: $response');
      }
      throw CustomException('Unexpected response');
    }
  }

  /// Submit two-factor authentication code
  Future<void> submitTwoFactorCode(String refCode, String twoFactorCode) async {
    final body = '03$refCode|$twoFactorCode';
    if (kDebugMode) {
      print('2FA submission: $body');
    }

    final response = await _makeRequest(body);
    final splitResponse = response.split('|');

    if (response == '0') {
      throw CustomException('Incorrect two-factor code');
    } else if (splitResponse.length == 2) {
      final tokens = AuthTokens.fromResponse(response);
      await _authService.storeTokens(tokens);
      await _authService.setTwoFactorEnabled(true);
    } else {
      throw CustomException('Unexpected response');
    }
  }

  /// Refresh access token using refresh token
  Future<String> refreshAccessToken() async {
    String? accessToken = await _authService.getValidAccessToken();
    if (accessToken != null && !_isTokenExpiring(accessToken)) {
      return accessToken;
    }

    await _updateApp();
    String? refreshToken = await _authService.getRefreshToken();
    if (refreshToken == null) {
      throw CustomException('Please login again');
    }

    final body = '04$refreshToken';
    final response = await _makeRequest(body);
    final splitResponse = response.split('|');

    if (response == '0') {
      throw CustomException('Redirecting to login page.. Please login again.');
    } else if (splitResponse.length == 2) {
      final tokens = AuthTokens.fromResponse(response);
      await _authService.storeTokens(tokens);
      return tokens.accessToken;
    } else {
      throw CustomException('Unexpected response');
    }
  }

  /// Sign up new user
  Future<int> signUp(String actCode, String fullName, String email, String phone) async {
    final body = '00$actCode|$fullName|$email|$phone';
    final response = await _makeRequest(body);

    try {
      await SmsAutoFill().listenForCode();
    } catch (e) {
      if (kDebugMode) {
        print("Error in parsing the OTP: $e");
      }
    }

    if (response == '0') {
      throw CustomException('Email already in use');
    }
    return int.parse(response);
  }

  /// Verify contact information during signup
  Future<String> verifyContact(String actCode, String password, String emailCode, String phoneCode) async {
    if (password.length < 8) {
      throw CustomException('Password must be at least 8 characters');
    }

    final passwordBase64 = base64.encode(utf8.encode(password));
    final body = '01$actCode|$passwordBase64|$emailCode|$phoneCode';
    final response = await _makeRequest(body);

    if (response == '0') {
      throw CustomException('Incorrect email or phone code');
    }
    return response;
  }

  /// Get user profile and token check
  Future<Map<dynamic, dynamic>> getProfile() async {
    if (ConfigurationCustom.skipAnyAuths) {
      return {
        "profile": {
          "email": "<EMAIL>",
          "emailVerified": true,
          "lastPassChange": "1720594664724",
          "lastUpdate": 1720967774113,
          "multiFactor": "0",
          "name": "John Doe",
          "phone": "+919845888888",
          "phoneVerified": true,
          "userID": "16170205-548a-487b-b9ee-4abdb624c550"
        },
        "projects": []
      };
    }

    const body = '07';
    final response = await _makeRequest(body, url: AuthConfig.au3Url);
    return jsonDecode(response);
  }

  /// Update user profile information
  Future<String> updateProfile(String oldPassword, String fullName, String email, String phone, String newPassword) async {
    final oldPassB64 = base64.encode(utf8.encode(oldPassword));
    final newPassB64 = base64.encode(utf8.encode(newPassword));

    final body = '00$oldPassB64|$fullName|$email|$phone|$newPassB64';
    final response = await _makeRequest(body, url: AuthConfig.au3Url);

    if (kDebugMode) {
      print('Profile update response: $response');
    }

    if (response == '0') {
      throw CustomException('Incorrect old password');
    } else if (response == '1') {
      throw CustomException('Email already in use');
    } else if (response == '2') {
      throw CustomException('Number already in use');
    } else {
      return "Success";
    }
  }

  /// Enable two-factor authentication
  Future<TwoFactorSetupResponse> enableTwoFactorAuth(int themeMode) async {
    final body = '02$themeMode';
    final response = await _makeRequest(body, url: AuthConfig.au3Url);

    if (response == '0') {
      throw CustomException('Failed to enable two-factor authentication');
    }

    return TwoFactorSetupResponse.fromResponse(response);
  }

  /// Disable two-factor authentication
  Future<void> disableTwoFactorAuth() async {
    const body = '03';
    await _makeRequest(body, url: AuthConfig.au3Url);
    await _authService.setTwoFactorEnabled(false);
  }

  /// Logout user
  Future<void> logout() async {
    const body = '05';
    try {
      await _makeRequest(body, url: AuthConfig.au3Url);
    } catch (e) {
      if (kDebugMode) {
        print('Logout API error (continuing anyway): $e');
      }
    }
    await _authService.clearAuthData();
  }

  /// Check if user is authenticated
  Future<bool> isAuthenticated() async {
    try {
      String? token = await _authService.getValidAccessToken();
      return token != null;
    } catch (e) {
      return false;
    }
  }

  /// Private helper methods
  Future<String> _makeRequest(String body, {String url = AuthConfig.au1Url, Duration? timeout}) async {
    final connectivityResult = await (Connectivity().checkConnectivity());
    if (connectivityResult.contains(ConnectivityResult.none)) {
      throw CustomException('No internet connection');
    }

    final jwt = (url == AuthConfig.au3Url) ? await _authService.getValidAccessToken() : null;
    String userAgent = await DeviceInfoUtil.getUserAgent();

    final headers = {
      'User-Agent': userAgent,
      'medium': 'phone',
      'Content-Type': 'text/plain',
      if (jwt != null) 'Authorization': 'Bearer $jwt',
      if (url == AuthConfig.au1Url) 'tenantID': AuthConfig.tenantId,
      if (url == AuthConfig.au1Url) 'clientID': AuthConfig.clientId,
    };

    final request = http.Request('POST', Uri.parse(url));
    request.headers.addAll(headers);
    request.body = body;

    final streamedResponse = await request.send().timeout(
      timeout ?? const Duration(seconds: 30),
    );

    if (streamedResponse.statusCode == 200) {
      return await streamedResponse.stream.bytesToString();
    } else {
      throw CustomException('Server error: ${streamedResponse.statusCode}');
    }
  }

  Future<void> _updateApp() async {
    if (Platform.isAndroid) {
      try {
        await InAppUpdate.checkForUpdate();
      } catch (e) {
        if (kDebugMode) {
          print('App update check failed: $e');
        }
      }
    }
  }

  bool _isTokenExpiring(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return true;

      final payload = json.decode(
        utf8.decode(base64Url.decode(base64Url.normalize(parts[1]))),
      );

      final exp = payload['exp'];
      if (exp == null) return true;

      final expiryDate = DateTime.fromMillisecondsSinceEpoch(exp * 1000);
      final now = DateTime.now();
      final timeUntilExpiry = expiryDate.difference(now);

      return timeUntilExpiry.inMinutes < 5;
    } catch (e) {
      return true;
    }
  }
}
