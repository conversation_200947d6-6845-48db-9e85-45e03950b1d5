import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:local_auth/local_auth.dart';
import '../models/auth_models.dart';

class AuthService {
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();
  static final LocalAuthentication _localAuth = LocalAuthentication();

  // Singleton pattern
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  /// Get a valid access token, refreshing if necessary
  Future<String?> getValidAccessToken() async {
    if (kDebugMode) {
      print("Getting valid access token");
    }

    try {
      String? accessToken =
          await _secureStorage.read(key: AuthConfig.accessTokenKey);
      if (accessToken == null || _isTokenExpired(accessToken)) {
        if (kDebugMode) {
          print("Access token expired or null, refreshing");
        }
        return await _refreshAccessToken();
      } else if (_isTokenExpiring(accessToken)) {
        // Refresh in background after 10 seconds
        if (kDebugMode) {
          print("Token expiring soon, scheduling refresh");
        }
        Future.delayed(const Duration(seconds: 10), () async {
          await _refreshAccessToken();
        });
        return accessToken;
      }

      if (kDebugMode) {
        print("Access token is valid");
      }
      return accessToken;
    } catch (e) {
      if (kDebugMode) {
        print("Error getting access token: $e");
      }
      throw Exception('Failed to get valid access token: $e');
    }
  }

  /// Store authentication tokens
  Future<void> storeTokens(AuthTokens tokens) async {
    await _secureStorage.write(
        key: AuthConfig.accessTokenKey, value: tokens.accessToken);
    await _secureStorage.write(
        key: AuthConfig.refreshTokenKey, value: tokens.refreshToken);
  }

  /// Store user credentials
  Future<void> storeCredentials(String email, String password) async {
    await _secureStorage.write(key: AuthConfig.emailKey, value: email);
    await _secureStorage.write(key: AuthConfig.passwordKey, value: password);
  }

  /// Get stored email
  Future<String?> getStoredEmail() async {
    return await _secureStorage.read(key: AuthConfig.emailKey);
  }

  /// Get stored password
  Future<String?> getStoredPassword() async {
    return await _secureStorage.read(key: AuthConfig.passwordKey);
  }

  /// Get refresh token
  Future<String?> getRefreshToken() async {
    return await _secureStorage.read(key: AuthConfig.refreshTokenKey);
  }

  /// Clear all stored authentication data
  Future<void> clearAuthData() async {
    await _secureStorage.delete(key: AuthConfig.accessTokenKey);
    await _secureStorage.delete(key: AuthConfig.refreshTokenKey);
    await _secureStorage.delete(key: AuthConfig.emailKey);
    await _secureStorage.delete(key: AuthConfig.passwordKey);
    await _secureStorage.delete(key: AuthConfig.twoFactorKey);
    await _secureStorage.delete(key: AuthConfig.biometricKey);
  }

  /// Two-factor authentication settings
  Future<void> setTwoFactorEnabled(bool enabled) async {
    await _secureStorage.write(
        key: AuthConfig.twoFactorKey, value: enabled.toString());
  }

  Future<bool> isTwoFactorEnabled() async {
    String? value = await _secureStorage.read(key: AuthConfig.twoFactorKey);
    return value == 'true';
  }

  /// Biometric authentication settings
  Future<void> setBiometricEnabled(bool enabled) async {
    await _secureStorage.write(
        key: AuthConfig.biometricKey, value: enabled.toString());
  }

  Future<bool> isBiometricEnabled() async {
    String? value = await _secureStorage.read(key: AuthConfig.biometricKey);
    return value == 'true';
  }

  /// Check if biometric authentication is available
  Future<bool> isBiometricSupported() async {
    try {
      return await _localAuth.canCheckBiometrics;
    } catch (e) {
      return false;
    }
  }

  /// Check if biometric authentication is set up
  Future<bool> isBiometricSetup() async {
    try {
      bool canCheckBiometrics = await isBiometricSupported();
      if (canCheckBiometrics) {
        List<BiometricType> availableBiometrics =
            await _localAuth.getAvailableBiometrics();
        return availableBiometrics.isNotEmpty;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Authenticate with biometrics
  Future<bool> authenticateWithBiometrics(
      {String reason = 'Authenticate to proceed'}) async {
    try {
      bool isSetup = await isBiometricSetup();
      if (!isSetup) return false;

      bool authenticated = await _localAuth.authenticate(
        localizedReason: reason,
        options: const AuthenticationOptions(
          useErrorDialogs: true,
          stickyAuth: true,
        ),
      );
      return authenticated;
    } catch (e) {
      if (kDebugMode) {
        print('Biometric authentication error: $e');
      }
      return false;
    }
  }

  /// Check if biometric login should be offered
  Future<String?> getBiometricLoginEmail() async {
    String? biometric = await _secureStorage.read(key: AuthConfig.biometricKey);
    String? email = await _secureStorage.read(key: AuthConfig.emailKey);

    if (kDebugMode) {
      print('biometric: $biometric, email: $email');
    }

    if (biometric != null && email != null && biometric == 'true') {
      return email;
    }
    return null;
  }

  /// Store biometric settings for user
  Future<void> checkAndStoreBiometric(String email, String password) async {
    String? storedEmail = await _secureStorage.read(key: AuthConfig.emailKey);
    if ((storedEmail != null && storedEmail != email) || storedEmail == null) {
      await _secureStorage.delete(key: AuthConfig.biometricKey);
    }
    await storeCredentials(email, password);
  }

  /// Private helper methods
  Future<String> _refreshAccessToken() async {
    // This method will be called by AuthRepository to avoid circular dependency
    // For now, we'll throw an exception to indicate token refresh is needed
    throw Exception('Token refresh required');
  }

  bool _isTokenExpired(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return true;

      final payload = json.decode(
        utf8.decode(base64Url.decode(base64Url.normalize(parts[1]))),
      );

      final exp = payload['exp'];
      if (exp == null) return true;

      final expiryDate = DateTime.fromMillisecondsSinceEpoch(exp * 1000);
      return DateTime.now().isAfter(expiryDate);
    } catch (e) {
      return true;
    }
  }

  bool _isTokenExpiring(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return true;

      final payload = json.decode(
        utf8.decode(base64Url.decode(base64Url.normalize(parts[1]))),
      );

      final exp = payload['exp'];
      if (exp == null) return true;

      final expiryDate = DateTime.fromMillisecondsSinceEpoch(exp * 1000);
      final now = DateTime.now();
      final timeUntilExpiry = expiryDate.difference(now);

      // Consider token expiring if less than 5 minutes remaining
      return timeUntilExpiry.inMinutes < 5;
    } catch (e) {
      return true;
    }
  }
}
